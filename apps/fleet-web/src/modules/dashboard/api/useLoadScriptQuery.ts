import * as Sentry from '@sentry/browser'
import { useQuery } from '@tanstack/react-query'

import {
  API_RETRY_TIMES,
  retryApiCall,
  SCRIPT_LOAD_TIMEOUT,
} from 'src/modules/shared/utils'
import { createQuery } from 'src/util-functions/react-query-utils'

import { reportDashboardErrorToSentry } from './queries'

function fetchScript(url: string): Promise<boolean> {
  if (ENV.CYPRESS_CT_ENV) {
    return Promise.resolve(true) // skip for cypress tests
  }

  const scriptToBeLoaded = document.querySelector(`script[src="${url}"]`)

  if (scriptToBeLoaded) {
    // NOTE: normally when the script is placed in the HTML, Sisense object should be loaded, but sometimes it's not
    if (window.Sisense) {
      return Promise.resolve(true) // already exists
    } else {
      scriptToBeLoaded.remove() // remove the script tag and reload it
    }
  }

  const now = Date.now()
  const scriptElement = document.createElement('script')
  scriptElement.src = url
  scriptElement.type = 'text/javascript'

  // Set up timeout for script loading
  let timeoutId: number | null = null
  let isResolved = false

  const cleanup = () => {
    if (timeoutId) {
      window.clearTimeout(timeoutId)
      timeoutId = null
    }
    // Remove event listeners to prevent memory leaks
    scriptElement.onload = null
    scriptElement.onerror = null
    scriptElement.onabort = null
  }

  return new Promise<boolean>((resolve, reject) => {
    // Handle successful script load
    scriptElement.onload = () => {
      if (isResolved) return
      isResolved = true
      cleanup()

      const loadTime = Date.now() - now
      Sentry.withScope((scope) => {
        scope.setTag('module', 'dashboard-sisense')
        Sentry.captureMessage(`sisense.js loaded in ${loadTime}ms.`)
      })
      resolve(true)
    }

    const handleError = (errorType: string, errorEvent?: Event | string) => {
      if (isResolved) return
      isResolved = true
      cleanup()

      // Remove the failed script element before rejecting
      scriptElement.remove()

      const errorMessage =
        `Failed to load the sisense.js file: '${url}', because of ${errorType}.` +
        errorEvent
          ? ` \n${typeof errorEvent === 'object' ? errorEvent.type : errorEvent}`
          : ''
      reportDashboardErrorToSentry({
        message: errorMessage,
      })
      reject(new Error(errorMessage))
    }

    // Handle script loading errors (network errors, 404, etc.)
    scriptElement.onerror = (event) => handleError('network error', event)

    // Handle script loading abort (user navigation, etc.)
    scriptElement.onabort = (event) => handleError('aborted', event)

    // Set up timeout to handle cases where the script hangs
    timeoutId = window.setTimeout(() => {
      handleError('timeout')
    }, SCRIPT_LOAD_TIMEOUT)

    // Append script to DOM to start loading
    try {
      document.body.appendChild(scriptElement)
    } catch {
      handleError('DOM append failed')
    }
  })
}

export const fetchScriptQueryKeyPrefix = ['dashboard/script_query'] as const

const fetchScriptQuery = ({
  name,
  url,
  enabled,
}: {
  name: string
  url: string
  enabled: boolean
}) =>
  createQuery({
    queryKey: [...fetchScriptQueryKeyPrefix, name] as const,
    queryFn: () => retryApiCall(() => fetchScript(url), API_RETRY_TIMES),
    enabled,
  })

export const useLoadScriptQuery = (props: {
  name: string
  url: string
  enabled: boolean
}) => useQuery(fetchScriptQuery(props))
