import * as Sentry from '@sentry/browser'
import { useQuery } from '@tanstack/react-query'

import { API_RETRY_TIMES, fetchScript, retryApiCall } from 'src/modules/shared/utils'
import { createQuery } from 'src/util-functions/react-query-utils'

import { reportDashboardErrorToSentry } from './queries'

// Sisense-specific wrapper that provides the callbacks for Sisense.js
const fetchSisenseScript = (url: string) =>
  fetchScript({
    url,
    isScriptLoaded: () => !!window.Sisense,
    onSuccess: (loadTime) => {
      Sentry.withScope((scope) => {
        scope.setTag('module', 'dashboard-sisense')
        Sentry.captureMessage(`sisense.js loaded in ${loadTime}ms.`)
      })
    },
    onError: (errorMessage) => {
      reportDashboardErrorToSentry({
        message: errorMessage,
      })
    },
    scriptName: 'sisense.js',
  })

export const fetchScriptQueryKeyPrefix = ['dashboard/script_query'] as const

const fetchScriptQuery = ({
  name,
  url,
  enabled,
}: {
  name: string
  url: string
  enabled: boolean
}) =>
  createQuery({
    queryKey: [...fetchScriptQueryKeyPrefix, name] as const,
    queryFn: () => retryApiCall(() => fetchSisenseScript(url), API_RETRY_TIMES),
    enabled,
  })

export const useLoadScriptQuery = (props: {
  name: string
  url: string
  enabled: boolean
}) => useQuery(fetchScriptQuery(props))
