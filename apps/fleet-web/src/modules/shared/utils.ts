import {
  matchQuery,
  type Query,
  type QueryClient,
  type QueryKey,
} from '@tanstack/react-query'

import { industrySelectionMetaDataQuery } from 'api/industry-selection/queries'
import { createTimelineHardwareTypeQueryKey } from 'api/timeline/useTimelineHardwareTypeQuery'
import { userStaticMetaDataQuery } from 'api/user/useUserStaticMetaData'
import { createGetClientReportPermissionsQueryKey } from 'src/modules/admin/shared/data-access/reports/api/useGetClientReportPermissionsQuery'
import { departmentsQuery } from 'src/modules/settings/company/api/useDepartmentsQuery'
import { rolesQuery } from 'src/modules/settings/manage-roles/api/queries'
import type { NonEmptyArray } from 'src/types/utils'

import { controlRoomCancellationReasonsQuery } from '../alert-center/api/useControlRoomCancellationReasonsQuery'
import { eventTypesQueryKeyPrefix } from '../api/useEventTypes'
import { npsScoreSelectionMetaDataQuery } from '../app/UserNpsScoreSelection/api/queries'
import { carpoolRulesQuery } from '../carpool/OLD_Settings/Rules/api/queries'
import { carpoolOptionsQuery } from '../carpool/queries/useCarpoolOptionsQuery'
import {
  carpoolCategoriesQuery,
  requestPurposesQuery,
} from '../carpool/Settings/VehicleManagement/api/queries'
import { coachingEventsTablesPreRequisitesQuery } from '../coaching/CoachingEvents/api/queries'
import { fetchScriptQueryKeyPrefix } from '../dashboard/api/useLoadScriptQuery'
import { deliveryCustomersQuery } from '../deliveryRevamp/api/customers/useDeliveryCustomers'
import { deliverySettingsQueryKey } from '../deliveryRevamp/api/settings/useDeliverySettingsQuery'
import { locationsQuery, locationTypesQuery } from '../lists/Facilities/api/queries'
import { customFormsPrerequisitesQuery } from '../maintenance/InspectionFormSetup/api/queries'
import { createVehiclePopoverAccessiblePartsQueryKey } from '../map-view/FleetMapView/LeftPanel/Vehicle/VehiclePopover/queries'
import { heatmapsVehicleEventTypesQuery } from '../map-view/HeatMapsMapView/api'
import { clusterOnboardingQuery } from '../map-view/VehicleCluster/api/queries'
import { companyProfileSettingsQuery } from '../settings/company/api/useCompanyProfileSettingsQuery'
import { companyVehicleSettingsOptionsQuery } from '../settings/company/api/useCompanyVehicleSettingsOptionsQuery'
import { userAvailableCameraTerminalTypesQuery } from '../vision/api/queries'

export const API_RETRY_TIMES = 2
export const SCRIPT_LOAD_TIMEOUT = 30000 // 30 seconds

export async function retryApiCall<T>(
  apiFunction: () => Promise<T>,
  maxRetries: number,
): Promise<T> {
  let retries = 0
  while (retries < maxRetries) {
    try {
      const result = await apiFunction()
      return result // Success, return the result
    } catch (error) {
      console.error(`API call failed (retry ${retries + 1}):`, error)
      retries++
    }
  }
  throw new Error(`API call failed after ${maxRetries} retries`)
}

/**
 * These types of queries never have anything to do with entity mutations and are often unnecessarily invalidated.
 * If you need to invalidate them, do so explicitly.
 *
 * __VERY IMPORTANT__
 * DOT NOT ADD OR REMOVE any queries without confirming it won't break any revalidation logic. CAN CAUSE MASSIVE BUGS.
 */
const entityMutationsQueryExceptions = [
  npsScoreSelectionMetaDataQuery(),
  userAvailableCameraTerminalTypesQuery(),
  { queryKey: fetchScriptQueryKeyPrefix },
  industrySelectionMetaDataQuery(),

  // A vehicle hardware type should not change, whatever the user does on fleet-web
  { queryKey: createTimelineHardwareTypeQueryKey('INVALIDATION') },

  // The vehicle popover parts don't depend on any user based entity mutations
  { queryKey: createVehiclePopoverAccessiblePartsQueryKey() },

  // Vehicle events available for heatmaps should not change based on user actions
  heatmapsVehicleEventTypesQuery(),

  // Coaching events tables pre-requisites should not change based on user actions
  coachingEventsTablesPreRequisitesQuery(),

  // These will not change based on user actions
  controlRoomCancellationReasonsQuery(),

  // This only contains "static" things that the user can't change
  customFormsPrerequisitesQuery(),

  clusterOnboardingQuery(),

  // These will not change based on user actions
  locationTypesQuery(),

  // This is a static query that doesn't depend on any user action
  { queryKey: eventTypesQueryKeyPrefix },

  // This is a static query that doesn't depend on any user action
  userStaticMetaDataQuery(),
] satisfies ReadonlyArray<{ queryKey: ReadonlyArray<any> }>

/**
 * Carefully chosen entities that can be mutated (created/edited/deleted/updated/affected) by the user, directly or indirectly.
 */
type MutableByUserEntity =
  | 'user'
  | 'driver'
  | 'vehicle'
  | 'geofence'
  | 'ruc_license'
  | 'dashboard_widget'
  | 'custom_form'
  | 'facility'
  | 'carpool_booking'
  | 'department'

function shouldInvalidateMatchQueryOfEntity({
  entity,
  query,
  extraExceptions,
}: {
  entity: MutableByUserEntity
  query: Query<unknown, Error, unknown, QueryKey>
  extraExceptions: ReadonlyArray<{ queryKey: ReadonlyArray<any> }>
}): boolean {
  /* ___IMPORTANT___
     Be very careful when adding exceptions here. It's better to over-invalidate than under-invalidate.
  */
  // TODO - In the future we should fine tune in the future more exceptions for each entity to prevent invalidation of expensive non related queries
  switch (entity) {
    case 'user': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
    }
    case 'driver': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, {
        // Make sure you know what you are doing if you add more exceptions here
        extraExceptions: [
          { queryKey: deliverySettingsQueryKey },
          deliveryCustomersQuery(),
          ...extraExceptions,
        ],
      })
    }
    case 'vehicle': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
    }
    case 'geofence': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, {
        /* These exceptions are here for two reasons:
           1. They are not affected by any geofence related change at the moment.
           2. We have some screens that are using these queries and we don't want to invalidate them when a geofence related query is changed

           E.g - Sub user page is one of the pages that benefits from this on my-data-access/geofences change
        */
        extraExceptions: [
          departmentsQuery(),
          rolesQuery(),
          { queryKey: createGetClientReportPermissionsQueryKey('INVALIDATION') },
          ...extraExceptions,
        ],
      })
    }
    case 'ruc_license': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
    }
    case 'dashboard_widget': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
    }
    case 'custom_form': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
    }
    case 'facility': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, {
        // Make sure you know what you are doing if you add more exceptions here
        extraExceptions: [
          requestPurposesQuery(),
          carpoolCategoriesQuery(),
          ...extraExceptions,
        ],
      })
    }
    case 'carpool_booking': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, {
        // Make sure you know what you are doing if you add more exceptions here
        extraExceptions: [
          carpoolOptionsQuery(),
          carpoolRulesQuery(),
          locationsQuery(),
          ...extraExceptions,
        ],
      })
    }
    case 'department': {
      return queryDoesNotMatchAnyEntityMutationsExceptions(query, {
        // Make sure you know what you are doing if you add more exceptions here
        extraExceptions: [
          companyProfileSettingsQuery(),
          companyVehicleSettingsOptionsQuery(),
          locationsQuery(),
          ...extraExceptions,
        ],
      })
    }
  }
}

const queryDoesNotMatchAnyEntityMutationsExceptions = (
  query: Query<unknown, Error, unknown, QueryKey>,
  opts: { extraExceptions: ReadonlyArray<{ queryKey: ReadonlyArray<any> }> },
) => {
  const extraExceptions = opts?.extraExceptions ?? []
  const matchesAtLeastOneException = [
    ...extraExceptions,
    ...entityMutationsQueryExceptions,
  ].some((queryException) => matchQuery(queryException, query))

  if (matchesAtLeastOneException) {
    return false
  }
  return true
}

/**
 *
 * See https://x.com/TkDodo/status/1744458937313771770?s=20
 *
 * Our standard and __safe__ way to invalidate queries on every entity mutations.
 *
 * Entity is defined as a resource that has a unique id. Let's say a vehicle, driver, widget, user, geofence, etc.
 *
 * __May overfetch in very specific situations so PAY ATTENTION__, but it's better than under fetching.
 *
 * If you want to add more exceptions you can pass an argument.
 */
export const invalidateQueriesOnEntitiesMutation = (
  queryClient: QueryClient,
  {
    entities,
    extraExceptions = [],
  }: {
    entities: NonEmptyArray<MutableByUserEntity> | 'all_standard'
    extraExceptions?: ReadonlyArray<{ queryKey: ReadonlyArray<any> }>
  },
) =>
  queryClient.invalidateQueries({
    predicate: (query) => {
      if (entities === 'all_standard') {
        return queryDoesNotMatchAnyEntityMutationsExceptions(query, { extraExceptions })
      }
      return entities.some((entity) =>
        shouldInvalidateMatchQueryOfEntity({ entity, query, extraExceptions }),
      )
    },
  })
